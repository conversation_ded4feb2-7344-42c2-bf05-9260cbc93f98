import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import GoogleAnalytics from './components/GoogleAnalytics';
import GoogleAdsense from './components/GoogleAdsense';
import Script from 'next/script';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Steal a Brainrot Secrets: Ultimate Roblox Guide & Strategies",
  description: "Master steal a brainrot secrets with our comprehensive guide! Discover steal a brainrot secrets names, advanced strategies, and steal a brainrot secrets list for Roblox domination. Your steal a brainrot secrets wiki awaits!",
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  alternates: {
    canonical: 'https://stealabrainrotsecrets.top',
  },
  openGraph: {
    type: 'website',
    url: 'https://stealabrainrotsecrets.top',
    title: 'Steal a Brainrot Secrets: Ultimate Roblox Guide',
    description: 'Discover the ultimate steal a brainrot secrets and master strategic gameplay! Learn steal a brainrot secrets names, advanced techniques, and steal a brainrot secrets new strategies for Roblox domination.',
    siteName: 'Steal a Brainrot Secrets',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Steal a Brainrot Secrets: Ultimate Roblox Guide',
    description: 'Master steal a brainrot secrets with our comprehensive guide and strategies!',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <Script
          defer
          data-domain="stealabrainrotsecrets.top"
          src="https://plausible.nancook.com/js/script.file-downloads.hash.outbound-links.pageview-props.revenue.tagged-events.js"
          strategy="beforeInteractive"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {/*<HeadScript />*/}
        <GoogleAnalytics />
        <GoogleAdsense />
        <Navbar />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
